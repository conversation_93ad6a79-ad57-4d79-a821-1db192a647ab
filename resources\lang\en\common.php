<?php

return [
    // Website basic information
    'site_name' => 'Online Store',
    'welcome' => 'Welcome',
    'home' => 'Home',
    'about' => 'About Us',
    'contact' => 'Contact',
    'login' => 'Login',
    'register' => 'Register',
    'logout' => 'Logout',
    
    // Navigation menu
    'categories' => 'Categories',
    'products' => 'Products',
    'new_arrivals' => 'New Arrivals',
    'hot_sales' => 'Hot Sales',
    'recommended' => 'Recommended',
    'search' => 'Search',
    'search_placeholder' => 'Search products...',
    
    // Shopping cart
    'cart' => 'Shopping Cart',
    'add_to_cart' => 'Add to Cart',
    'buy_now' => 'Buy Now',
    'quantity' => 'Quantity',
    'price' => 'Price',
    'total' => 'Total',
    'checkout' => 'Checkout',
    'cart_empty' => 'Your cart is empty',
    
    // Product related
    'product_details' => 'Product Details',
    'product_description' => 'Product Description',
    'product_specifications' => 'Product Specifications',
    'in_stock' => 'In Stock',
    'out_of_stock' => 'Out of Stock',
    'add_to_wishlist' => 'Add to Wishlist',
    'share' => 'Share',
    
    // User related
    'my_account' => 'My Account',
    'profile' => 'Profile',
    'orders' => 'Orders',
    'order_history' => 'Order History',
    'wishlist' => 'Wishlist',
    'addresses' => 'Addresses',
    'password' => 'Password',
    'email' => 'Email',
    'phone' => 'Phone',
    'name' => 'Name',
    
    // Order related
    'order_number' => 'Order Number',
    'order_date' => 'Order Date',
    'order_status' => 'Order Status',
    'shipping_address' => 'Shipping Address',
    'billing_address' => 'Billing Address',
    'payment_method' => 'Payment Method',
    'shipping_method' => 'Shipping Method',
    
    // Status
    'pending' => 'Pending',
    'processing' => 'Processing',
    'shipped' => 'Shipped',
    'delivered' => 'Delivered',
    'cancelled' => 'Cancelled',
    
    // Common buttons
    'save' => 'Save',
    'cancel' => 'Cancel',
    'edit' => 'Edit',
    'delete' => 'Delete',
    'view' => 'View',
    'back' => 'Back',
    'next' => 'Next',
    'previous' => 'Previous',
    'submit' => 'Submit',
    'confirm' => 'Confirm',
    
    // Messages
    'success' => 'Success',
    'error' => 'Error',
    'warning' => 'Warning',
    'info' => 'Information',
    'loading' => 'Loading...',
    'no_results' => 'No results found',
    'try_again' => 'Try again',
    
    // Pagination
    'showing' => 'Showing',
    'of' => 'of',
    'results' => 'results',
    'per_page' => 'per page',
    
    // Currency
    'currency' => 'USD',
    'usd' => 'US Dollar',
    
    // Contact information
    'address' => 'Address',
    'city' => 'City',
    'country' => 'Country',
    'postal_code' => 'Postal Code',
    
    // Language switching
    'language' => 'Language',
    'select_language' => 'Select Language',
    'auto_detected' => 'Auto Detected',
];
