<!doctype html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
        <meta name="renderer" content="webkit">
        <meta name="keywords" content="{{$_meta_keywords ?? ''}}">
        <meta name="description" content="{{$_meta_description ?? ''}}">
        <!-- CSRF Token -->
        <meta name="csrf-token" content="{{ csrf_token() }}">
        <title>@if(isset($_title)){{$_title}} - {{ config('strongshop.storeName') }}@else {{app('strongshop')->getShopConfig('store_title')}} @endif</title>
        <!-- Styles -->
        <link rel="stylesheet" href="{{ asset('css/bootstrap.min.css') }}">
        <link rel="stylesheet" href="{{ asset('css/bootstrap-theme.min.css') }}">
        <link rel="stylesheet" href="{{ asset('css/bootstrap-icons.css') }}">
        <link rel="stylesheet" href="{{ asset('css/main.css') }}?v={{env('APP_VERSION')}}">

        <!-- Scripts 兼容 ie8 自适应 -->
        <script src="{{ asset('js/vendor/modernizr-2.8.3-respond-1.4.2.min.js') }}"></script>

        <!-- 阻止外部字体加载，解决CORS问题 -->
        <script>
        (function() {
            // 安全的字体阻止函数
            function blockExternalFonts() {
                try {
                    // 移除现有的外部字体链接
                    const externalFontLinks = document.querySelectorAll('link[href*="shiptobuy.com"], link[href*="fonts.googleapis.com"]');
                    externalFontLinks.forEach(function(link) {
                        console.warn('移除外部字体链接:', link.href);
                        link.remove();
                    });

                    // 设置观察器（如果支持）
                    if (typeof MutationObserver !== 'undefined' && document.head) {
                        const observer = new MutationObserver(function(mutations) {
                            mutations.forEach(function(mutation) {
                                mutation.addedNodes.forEach(function(node) {
                                    if (node.nodeType === 1 && node.tagName === 'LINK') {
                                        if (node.href && (node.href.includes('shiptobuy.com') || node.href.includes('fonts.googleapis.com'))) {
                                            console.warn('阻止外部字体加载:', node.href);
                                            node.remove();
                                        }
                                    }
                                });
                            });
                        });

                        observer.observe(document.head, {
                            childList: true,
                            subtree: true
                        });
                    }
                } catch (error) {
                    console.warn('字体阻止功能错误:', error);
                }
            }

            // 立即执行一次
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', blockExternalFonts);
            } else {
                blockExternalFonts();
            }
        })();
        </script>
        <!-- Google Tag Manager - 条件加载 -->
        <script>
        // 检查Cookie同意状态
        function loadGoogleTagManager() {
            if (localStorage.getItem('cookieConsent') === 'accepted') {
                (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
                new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
                j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
                'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
                })(window,document,'script','dataLayer','GTM-NNHXPH7G');
            }
        }

        // 页面加载时检查
        document.addEventListener('DOMContentLoaded', loadGoogleTagManager);
        </script>
        <!-- End Google Tag Manager -->
        <!--统计代码 - 条件加载-->
        <script>
        function loadStatisticalCode() {
            if (localStorage.getItem('cookieConsent') === 'accepted') {
                try {
                    const statisticalCode = {!! json_encode(app('strongshop')->getShopConfig('statistical_code') ?? '') !!};
                    if (statisticalCode && statisticalCode.trim()) {
                        document.head.insertAdjacentHTML('beforeend', statisticalCode);
                    }
                } catch (error) {
                    console.warn('统计代码加载错误:', error);
                }
            }
        }

        // 页面加载时检查
        document.addEventListener('DOMContentLoaded', loadStatisticalCode);
        </script>
        @stack('styles')
        @stack('scripts')
        <style>
/* 视频缩略图样式 */
.video-thumb {
    position: relative;
    cursor: pointer;
    list-style: none;
}
.video-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%,-50%);
    color: white;
    font-size: 24px;
    text-shadow: 0 0 5px rgba(0,0,0,0.5);
    pointer-events: none;
}
</style>

<script>
document.addEventListener("DOMContentLoaded", function(){
    // 缩略图点击切换
    document.querySelectorAll('.st-detail-img-left li').forEach(thumb => {
        thumb.addEventListener('click', function() {
            // 移除所有激活状态
            document.querySelectorAll('.st-detail-img-left li').forEach(li => li.classList.remove('active'));
            this.classList.add('active');

            // 主图切换
            const mainContainer = document.querySelector('.pic');
            if (this.classList.contains('video-thumb')) {
                // 显示视频
                mainContainer.innerHTML = `
                    <video 
                        id="mainVideo"
                        muted loop controls playsinline
                        poster="${this.querySelector('video').poster}"
                        style="width:100%"
                    >
                        <source src="${this.querySelector('source').src}" type="video/mp4">
                    </video>
                    <div class="magnify"></div>
                `;
                mainContainer.querySelector('video').play();
            } else {
                // 显示图片
                const imgSrc = this.querySelector('img').dataset.src;
                mainContainer.innerHTML = `
                    <img src="${imgSrc}" alt="">
                    <div class="magnify"></div>
                `;
            }
        });
    });

    // 自动播放视频缩略图
    document.querySelectorAll('.video-thumb video').forEach(v => {
        v.play().catch(() => v.load()); // 处理自动播放限制
    });
});
</script>
<script type="text/javascript">
    // Microsoft Clarity - 条件加载
    function loadMicrosoftClarity() {
        if (localStorage.getItem('cookieConsent') === 'accepted') {
            (function(c,l,a,r,i,t,y){
                c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
                t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
                y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
            })(window, document, "clarity", "script", "reo4jc7ny6");
        }
    }

    // 页面加载时检查
    document.addEventListener('DOMContentLoaded', loadMicrosoftClarity);
</script>
    </head>
    <body id="app" class="st">







        <!--Cookie同意横幅-->
        <div id="cookie-consent-banner" style="display: none; position: fixed; bottom: 0; left: 0; right: 0; background: #333; color: #fff; padding: 15px; z-index: 10000; box-shadow: 0 -2px 10px rgba(0,0,0,0.3);">
            <div class="container" style="display: flex; align-items: center; justify-content: space-between; flex-wrap: wrap; gap: 15px;">
                <div style="flex: 1; min-width: 300px;">
                    <p style="margin: 0; font-size: 14px; line-height: 1.4;">
                        We use cookies to improve your experience and for analytics. By continuing to browse, you agree to our use of cookies.
                        <a href="/privacy-policy" style="color: #4CAF50; text-decoration: underline;">Learn more</a>
                    </p>
                </div>
                <div style="display: flex; gap: 10px; flex-shrink: 0;">
                    <button id="cookie-decline" style="background: transparent; border: 1px solid #666; color: #fff; padding: 8px 16px; border-radius: 4px; cursor: pointer; font-size: 13px;">
                        Decline
                    </button>
                    <button id="cookie-accept" style="background: #4CAF50; border: none; color: #fff; padding: 8px 16px; border-radius: 4px; cursor: pointer; font-size: 13px;">
                        Accept All
                    </button>
                </div>
            </div>
        </div>

        <!--顶部-提示信息-->
        <div class="st-navtip">
            @if(!isset($_COOKIE['strongshop_browserOutdated']))
            <!--[if lte IE 8]>
            <div class="container">
                <div class="alert alert-warning alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span></button>
                    <p data-cookie="strongshop_browserOutdated">You are using an <strong>outdated</strong> browser. Please <a href="https://www.google.com/chrome/">upgrade your browser</a> to improve your experience.</p>
                </div>
            </div>
            <![endif]-->
            @endif
            @if(app('strongshop')->getShopConfig('notice') && !isset($_COOKIE['strongshop_notice']))
            <div class="container">
                <div class="alert alert-warning alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span></button>
                    <p data-cookie="strongshop_notice">{!! app('strongshop')->getShopConfig('notice') !!}</p>
                </div>
            </div>
            @endif
        </div>
        <!--导航区域-顶部 * 移动端隐藏-->
        <div class="st-navtop hidden-xs">
            <div class="container">
                <ul class="nav nav-pills pull-left st-navtop-items">
                    @if(false)
                    <li>
                        <div id="st-google-translate-element"></div>
                    </li>
                    @else
                    <li class="dropdown st-navtop-items-account">
                        <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                            <i class="glyphicon glyphicon-globe"></i>
                            <font id="current-language">🇰🇭 ភាសាខ្មែរ</font><span class="caret"></span>
                        </a>
                        <ul class="dropdown-menu" id="language-menu">
                            <!-- 东南亚主要语言 -->
                            <li><a href="javascript:selectLanguage('khmer', '🇰🇭 ភាសាខ្មែរ');" style="font-weight: bold;">🇰🇭 ភាសាខ្មែរ</a></li>
                            <li role="separator" class="divider"></li>
                            <li><a href="javascript:selectLanguage('thai', '🇹🇭 ภาษาไทย');">🇹🇭 ภาษาไทย</a></li>
                            <li><a href="javascript:selectLanguage('vietnamese', '🇻🇳 Tiếng Việt');">🇻🇳 Tiếng Việt</a></li>
                            <li><a href="javascript:selectLanguage('malay', '🇲🇾 Bahasa Melayu');">🇲🇾 Bahasa Melayu</a></li>
                            <li><a href="javascript:selectLanguage('indonesian', '🇮🇩 Bahasa Indonesia');">🇮🇩 Bahasa Indonesia</a></li>
                            <li role="separator" class="divider"></li>
                            <li><a href="javascript:selectLanguage('chinese_simplified', '🇨🇳 简体中文');">🇨🇳 简体中文</a></li>
                            <li><a href="javascript:selectLanguage('english', '🇺🇸 English');">🇺🇸 English</a></li>

                        </ul>
                    </li>
                    @endif
                    <li class="dropdown st-navtop-items-account">
                        <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                            <font>{{$_current_currency_name}}</font>
                            <!--<span class="caret"></span>-->
                            <span aria-hidden="true" style="color: rgb(118, 118, 118);">▼</span>
                        </a>
                        <ul class="dropdown-menu">
                            @foreach($_currencies as $currency)
                            <li @if($currency['code'] == app('strongshop')->getCurrentCurrency()) class="active" @endif>
                                <a rel="nofollow" href="{{request()->fullUrlWithQuery(['currency'=>$currency['code']])}}" rel="nofollow">{{$currency['name']}}</a>
                            </li>
                            @endforeach
                        </ul>
                    </li>
                </ul>
                <ul class="nav nav-pills pull-right st-navtop-items">
                    <li>
                        <a href="{{route('user.my.feedback')}}" rel="nofollow">
                            <i class="glyphicon glyphicon-question-sign"></i><font>@lang('Feedback Us')</font>
                        </a>
                    </li>
                    <li>
                        <a href="{{route('user.orderTracking')}}" rel="nofollow">
                            <i class="glyphicon glyphicon-map-marker"></i><font>@lang('Order Tracking')</font>
                        </a>
                    </li>
                    <li class="hidden-sm">
                        <a href="{{route('shoppingcart')}}" rel="nofollow">
                            <i class="glyphicon glyphicon-shopping-cart"></i>
                            <font>@lang('Shopping Cart')
                            (<span class="st-cartnum">@if($_cart['total']['cart_qty_total']>99)99+@else{{$_cart['total']['cart_qty_total']}}@endif</span>)
                            </font>
                        </a>
                    </li>
                    <li>
                        <a href="{{route('user.my.collects')}}" rel="nofollow">
                            <i class="glyphicon glyphicon-heart-empty"></i><font>@lang('Wish List')(<span id="ST-WISH-LIST-TOTAL">{{$_wish_list_total}}</span>)</font>
                        </a>
                    </li>
                    @guest
                    <li>
                        <a href="{{route('login')}}" rel="nofollow">
                            <i class="glyphicon glyphicon-log-in"></i><font>@lang('Sign in')</font>
                        </a>
                    </li>
                    @endguest
                    <li class="dropdown st-navtop-items-account">
                        <a href="{{route('user.index')}}" class="dropdown-toggle" data-hover="dropdown">
                            <i class="glyphicon glyphicon-user"></i>
                            <font>@lang('My Account')
                            @auth
                            ({{auth()->user()->nickname}})
                            <span class="badge">{{$_unread_feedback_replies_total}}</span>
                            @endauth
                            </font>
                            <span class="caret"></span>
                        </a>
                        <ul class="dropdown-menu">
                            @guest
                            <li><a href="{{route('login')}}" rel="nofollow">@lang('Sign in')</a></li>
                            <li><a href="{{route('register')}}" rel="nofollow">@lang('Sign up')</a></li>
                            <li role="separator" class="divider"></li>
                            @endguest
                            <li><a href="{{route('user.index')}}" rel="nofollow">@lang('User Home')</a></li>
                            <li><a href="{{route('user.my.orders')}}" rel="nofollow">@lang('My Orders')</a></li>
                            <li><a href="{{route('user.my.collects')}}" rel="nofollow">@lang('My Wish List')</a></li>
                            <li><a href="{{route('user.my.feedback')}}" rel="nofollow">@lang('My Feedback') <span class="badge">{{$_unread_feedback_replies_total}}</span></a></li>
                            @auth
                            <li role="separator" class="divider"></li>
                            <li><a rel="nofollow" href="{{route('logout')}}"><i class="glyphicon glyphicon-log-out"></i><font>@lang('Sign out')</font></a></li>
                            @endauth
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
        <!--导航区域-品牌、搜索和购物车 * 移动端隐藏-->
        <div class="st-navbrand hidden-xs">
            <div class="container">
                <div class="row">
                    <div class="col-sm-3">
                        <div class="st-navbrand-logo">
                            <a href="/"><img src="{{ asset('img/logo.272x92.png') }}"class="img-responsive" alt="{{config('app.name')}}"  title="{{config('app.name')}}" /></a>
                        </div>
                    </div>
                    <div class="col-sm-2">
                        <!--<div class="st-navbrand-slogan hidden-sm">@lang('Slogan')</div>-->
                    </div>
                    <div class="col-sm-5">
                        <form id="ST-SEARCH" method="get" action="{{route('product.list')}}">
                            <div class="input-group st-navbrand-search">
                                <input name="keywords" type="text" class="form-control" placeholder="@lang('Search Products')" required="required" value="{{request('keywords')}}" />
                                <div class="input-group-addon" onclick="document.getElementById('ST-SEARCH').submit();">
                                    <i class="glyphicon glyphicon-search"></i>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="col-sm-2">
                        <div id="ST-NAVCART-ICON"  class="st-navbrand-cart pull-right">
                            <a rel="nofollow" href="{{route('shoppingcart')}}"><i class="glyphicon glyphicon-shopping-cart"></i>Cart</a>
                            <span class="badge st-cartnum">@if($_cart['total']['cart_qty_total']>99) 99+ @else {{$_cart['total']['cart_qty_total']}} @endif</span>
                        </div>
                        @if(!request()->route()->named(['shoppingcart', 'shoppingcart.checkout']))
                        <!--导航区域-购物车-->
                        <div id="ST-NAVCART-PRODUCTS">
                            <div class="page-header st-navbrand-cart-total">
                                @include('layouts.includes.shoppingcartBtn')
                            </div>
                            <div class="st-navbrand-cart-product-list st-cart-product-list">
                                @include('layouts.includes.shoppingcart')
                            </div>
                        </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
        <!--导航区域-菜单 * 移动端隐藏-->
        <div class="st-navmenu hidden-xs">
            <div class="container">
                <ul class="nav nav-pills">
                    <li ><a href="/">@lang('Home')</a></li>
                    <li id="products">
                        <a href="{{route('product.list')}}">@lang('Product Categories')</a>
                        <div class="st-allcat panel panel-default">
                            <!-- List 一级分类 -->
                            <ul class="list-group st-allcat-items">
                                @foreach($_categories as $category)
                                <li class="list-group-item">
                                    <a href="{{route('product.list.rewrite', ['catid'=>$category->id])}}">
                                        <i class="bi-life-preserver"></i>
                                        <font>{{$category->name}}</font>
                                    </a>
                                </li>
                                @endforeach
                            </ul>
                            <div class="st-allcat-content">
                                @foreach($_categories as $category)
                                <div class="st-allcat-content-item">
                                    @foreach($category->children as $child)
                                    <dl>
                                        <!--二级分类-->
                                        <dt><a href="{{route('product.list.rewrite', ['catid'=>$child->id])}}">{{$child['name']}}</a></dt>
                                        @foreach($child['children'] as $childChild)
                                        <!--三级分类-->
                                        <dd><a href="{{route('product.list.rewrite', ['catid'=>$childChild->id])}}">{{$childChild['name']}}</a></dd>
                                        @endforeach
                                    </dl>
                                    @endforeach
                                </div>
                                @endforeach
                            </div>
                        </div>
                    </li>
                
                   <!-- <li ><a href="#">Women</a></li>
                    <li ><a href="#">Man</a></li>
                    <li ><a href="#">@lang('Promotion')</a></li>-->
                    <li ><a href="{{route('user.my.feedback')}}">@lang('Feedback Us')</a></li>
                </ul>
            </div>
        </div>
        <!-- 头部导航 * 移动端显示 -->
        <nav class="navbar navbar-default st-header visible-xs-block">
            <div class="container-fluid">
                <!-- Brand and toggle get grouped for better mobile display -->
                <div class="row">
                    <div class="navbar-header">
                        <div class="col-xs-2">
                            <button type="button" class="navbar-toggle collapsed pull-left" data-toggle="collapse" data-target="#nav-product-categories" aria-expanded="false">
                                <span class="icon-bar"></span><span class="icon-bar"></span><span class="icon-bar"></span>
                            </button>
                        </div>
                        <div class="col-xs-6 navbar-brand-container">
                            <a class="navbar-brand" href="/" style="display: block; max-width: 272px; padding:5px 0;">
                                <img src="{{asset('img/logo.272x92.png')}}" class="img-responsive" alt="{{config('app.name')}}"  title="{{config('app.name')}}" style="width: 100%; height: auto; max-height: 92px; object-fit: contain; display: block; margin: 0 auto;"/>
                            </a>
                        </div>
                        <div class="col-xs-4">
                            <span class="bi bi-person-circle pull-right st-nav-user" data-toggle="collapse" data-target="#nav-user" aria-expanded="true"></span>
                            <a href="{{route('shoppingcart')}}" class="st-header-cart pull-right">
                                <i class="glyphicon glyphicon-shopping-cart"></i>
                                <span class="st-cartnum">@if($_cart['total']['cart_qty_total']>99) 99+ @else {{$_cart['total']['cart_qty_total']}} @endif</span>
                            </a>
                        </div>
                    </div>
                </div>
                <!-- Collect the nav links, forms, and other content for toggling -->
                <div class="collapse navbar-collapse" id="nav-product-categories">
                    <form class="navbar-form" id="ST-SEARCH-M" method="get" action="{{route('product.list')}}">
                        <div class="input-group">
                            <input type="text" name="keywords" class="form-control" placeholder="@lang('Search Products')" required="required" value="{{request('keywords')}}">
                            <div class="input-group-addon" onclick="document.getElementById('ST-SEARCH-M').submit();"><i class="glyphicon glyphicon-search"></i></div>
                        </div>
                    </form>
                    <ul class="nav navbar-nav">
                        <li class="dropdown">
                            <a href="#" class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="true">@lang('Product Categories')<span class="caret"></span></a>
                            <ul class="dropdown-menu open st-cat">
                                <!--一级分类-->
                                @foreach($_categories as $category)
                                <li class="dropdown">
                                    <a href="{{route('product.list.rewrite', ['catid'=>$category->id])}}">{{$category->name}}</a>
                                    <ul class="dropdown-menu show st-subcat">
                                        <!--二级分类-->
                                        @foreach($category->children as $child)
                                        <li class="dropdown">
                                            <a href="{{route('product.list.rewrite', ['catid'=>$child->id])}}">{{$child->name}}</a>
                                            <ul class="dropdown-menu show st-subsubcat">
                                                <!--三级分类-->
                                                @foreach($child['children'] as $childChild)
                                                <li><a href="{{route('product.list.rewrite', ['catid'=>$childChild->id])}}">{{$childChild->name}}</a></li>
                                                @endforeach
                                            </ul>
                                        </li>
                                        @endforeach
                                    </ul>
                                </li>
                                @endforeach
                            </ul>
                        </li>
                    </ul>
                </div><!-- /.navbar-collapse -->
                <!-- Collect the nav links, forms, and other content for toggling -->
                <div class="collapse navbar-collapse" id="nav-user">
                    <ul class="nav navbar-nav">
                        <li class="dropdown"><a href="/">@lang('Home')</a></li>
                        @guest
                        <li class="dropdown"><a rel="nofollow" href="{{route('login')}}">@lang('Sign in')</a></li>
                        <li class="dropdown"><a rel="nofollow" href="{{route('register')}}">@lang('Sign up')</a></li>
                        @endguest
                        <li>
                            <a rel="nofollow" href="{{route('user.index')}}">
                                @lang('My Account')
                                @auth
                                ({{auth()->user()->nickname}})
                                @endauth
                            </a>
                        </li>
                        <li><a rel="nofollow" href="{{route('user.my.orders')}}">@lang('My Orders')</a></li>
                        <li><a rel="nofollow" href="{{route('user.my.collects')}}">@lang('My Wish List')</a></li>
                        <li><a rel="nofollow" href="{{route('user.my.feedback')}}">@lang('My Feedback') <span class="badge">{{$_unread_feedback_replies_total}}</span></a></li>
                        <li role="separator" class="divider"></li>
                        <li class="dropdown"><a rel="nofollow" href="/article-53.html">@lang('Contact Us')</a></li>
                        <li class="dropdown"><a rel="nofollow" href="{{route('user.my.feedback')}}">@lang('Feedback us')</a></li>
                        <li role="separator" class="divider"></li>
                        <li class="dropdown st-navtop-items-account">
                            <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                                <i class="glyphicon glyphicon-globe"></i>
                                <font id="current-language-mobile">🇰🇭 ភាសាខ្មែរ</font><span class="caret"></span>
                            </a>
                            <ul class="dropdown-menu">
                                <!-- 东南亚主要语言 -->
                                <li><a href="javascript:selectLanguage('khmer', '🇰🇭 ភាសាខ្មែរ');" style="font-weight: bold;">🇰🇭 ភាសាខ្មែរ</a></li>
                                <li role="separator" class="divider"></li>
                                <li><a href="javascript:selectLanguage('thai', '🇹🇭 ภាษាไทย');">🇹🇭 ภាษាไทย</a></li>
                                <li><a href="javascript:selectLanguage('vietnamese', '🇻🇳 Tiếng Việt');">🇻🇳 Tiếng Việt</a></li>
                                <li><a href="javascript:selectLanguage('malay', '🇲🇾 Bahasa Melayu');">🇲🇾 Bahasa Melayu</a></li>
                                <li><a href="javascript:selectLanguage('indonesian', '🇮🇩 Bahasa Indonesia');">🇮🇩 Bahasa Indonesia</a></li>
                                <li role="separator" class="divider"></li>
                                <li><a href="javascript:selectLanguage('chinese_simplified', '🇨🇳 简体中文');">🇨🇳 简体中文</a></li>
                                <li><a href="javascript:selectLanguage('english', '🇺🇸 English');">🇺🇸 English</a></li>
                            </ul>
                        </li>
                        <li class="dropdown st-navtop-items-account">
                            <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                                <font>{{$_current_currency_name}}</font><span class="caret"></span>
                            </a>
                            <ul class="dropdown-menu">
                                @foreach($_currencies as $currency)
                                <li @if($currency['code'] == app('strongshop')->getCurrentCurrency()) class="active" @endif>
                                    <a rel="nofollow" href="{{request()->fullUrlWithQuery(['currency'=>$currency['code']])}}">{{$currency['name']}}</a>
                                </li>
                                @endforeach
                            </ul>
                        </li>
                        @auth
                        <li role="separator" class="divider"></li>
                        <li><a rel="nofollow" href="{{route('logout')}}"><i class="glyphicon glyphicon-log-out"></i><font>@lang('Sign out')</font></a></li>
                        @endauth

                    </ul>
                </div><!-- /.navbar-collapse -->
            </div>
        </nav>

        @include('layouts.includes.errors')

        @yield('content')
        <!--        <div class="st-translate">
                    <div class="st-h100"></div>
                    <div class="container">
                        <p id="google_translate_element" class="pull-right"></p>
                    </div>
                </div>-->
        <!--底部信息-->
        <div class="st-footer">
            <div class="st-footer-service">
                <div class="container">
                    <div class="row">
                        <div class="col-sm-6 col-md-3">
                            <dl>
                                <dt>@lang('Services')</dt>
                                <dd><a href="{{route('user.my.feedback')}}">@lang('Feedback')</a><dd>
                                <dd><a href="{{route('article.show.postid', ['postid'=>'privacy'])}}">@lang('Privacy & Security')</a><dd>
                            </dl>
                        </div>
                   <!--     <div class="col-sm-6 col-md-3">
                            <dl>
                                <dt>@lang('Shopping with us')</dt>
                                <dd><a href="{{route('article.show.postid', ['postid'=>'delivery'])}}">@lang('Delivery')</a><dd>
                                <dd><a href="{{route('article.show.postid', ['postid'=>'returns'])}}">@lang('Returns')</a><dd>
                            </dl>
                        </div>-->
                        <div class="col-sm-6 col-md-3">
                            <dl>
                                <dt>@lang('Customer Support')</dt>
                                <dd><a href="{{route('article.show.postid', ['postid'=>'aboutus'])}}">@lang('About us')</a><dd>
                                <dd><a href="{{route('article.show.postid', ['postid'=>'contactus'])}}">@lang('Contact us')</a><dd>
                            </dl>
                        </div>
                        <!--<div class="col-sm-6 col-md-3">
                            <dl>
                                <dt>@lang('Connect with us')</dt>
                                <dd class="st-footer-service-icon">
                                    <a href="#" >
                                        <i class="bi-facebook"></i>
                                    </a>
                                    <a href="#" >
                                        <i class="bi-instagram"></i>
                                    </a>
                                    <a href="#" >
                                        <i class="bi-pinterest"><svg t="1624281165955" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3033" width="22" height="20"><path d="M512 0.040958c-282.75605 0-511.979521 229.223471-511.979521 511.979521 0 216.895004 134.937323 402.149674 325.393704 476.75533-4.484941-40.50782-8.519339-102.641654 1.781689-146.876685 9.297548-39.954882 60.044958-254.49478 60.044958-254.49478s-15.318427-30.657334-15.318427-75.99824c0-71.185633 41.265549-124.329107 92.647814-124.329107 43.682093 0 64.775649 32.787169 64.775649 72.107196 0 43.927843-27.954082 109.604576-42.412384 170.448222-12.062238 50.972681 25.558018 92.524939 75.813927 92.524939 91.00948 0 160.945882-95.965441 160.945882-234.466141 0-122.588376-88.080957-208.293748-213.864085-208.293748-145.668413 0-231.168993 109.276909-231.168993 222.178633 0 44.00976 16.956762 91.193792 38.091276 116.833727 4.177753 5.078837 4.792128 9.522819 3.542898 14.683573-3.891044 16.178553-12.512779 50.952202-14.212551 58.078957-2.232231 9.358986-7.413463 11.345466-17.120595 6.840046-63.956482-29.776729-103.931843-123.264189-103.931843-198.340866 0-161.49882 117.345706-309.829527 338.275109-309.829527 177.595456 0 315.625135 126.561338 315.625135 295.698892 0 176.448622-111.24291 318.451262-265.655934 318.451262-51.873765 0-100.655174-26.950602-117.345706-58.795728 0 0-25.680893 97.767609-31.886085 121.707772-11.570737 44.460302-42.76053 100.204632-63.649294 134.220551 47.900804 14.826927 98.812048 22.834287 151.607376 22.834287 282.75605 0 511.979521-229.223471 511.979521-511.979521s-229.223471-511.979521-511.979521-511.979521z" p-id="3034" fill="#707070"></path></svg></i>
                                    </a>
                                </dd>
                                <dd class="st-footer-service-signup">
                                    <p>@lang('Sign Up for Our Newsletter'):</p>
                                    <div class="input-group">
                                        <input type="text" class="form-control" placeholder="Enter your email">
                                        <span class="input-group-addon" id="basic-addon2">@lang('Subscribe')</span>
                                    </div>
                                </dd>
                            </dl>
                        </div>-->
                    </div>
                </div>
            </div>
            <!--备案信息-->
            <div class="st-footer-beian">
                <div class="container">
                    &copy; {{date('Y')}} {{config('app.name')}} Copyright, All Rights Reserved. Powered By <a target="_blank" href="http://www.shiptobuy.com">{{config('app.name')}} </a>
                </div>
             
            </div>
            <div class="st-h10"></div>
        </div>
        <!-- Scripts -->
        <script src="{{ asset('js/vendor/jquery-1.11.2.min.js') }}"></script>
        <script src="{{ asset('js/vendor/jquery.form.min.js') }}"></script>
        <script src="{{ asset('js/vendor/jquery.cookie.js') }}"></script>
        <script src="{{ asset('js/vendor/bootstrap.min.js') }}"></script>
        <script src="{{ asset('js/vendor/bootstrap-hover-dropdown.js') }}"></script>
        <script src="{{ asset('plugins/layer/layer.js') }}"></script>
        <script src="{{ asset('js/main.js') }}?v={{env('APP_VERSION')}}"></script>
        @if(env('BlockSimpliedChineseBrowerVisitByJS'))
        <script>
                                if (Util.maybe360Browser()) {
                                    console.log('360Browser');
                                    window.location.href = 'http://www.baidu.com';
                                }
        </script>
        @endif
        <script>
        // 全局错误处理
        window.addEventListener('error', function(e) {
            console.warn('JavaScript错误:', e.error);
            return true; // 阻止错误冒泡
        });

        // 安全的初始化
        try {
            //js 初始化
            if (typeof Util !== 'undefined' && Util.init) Util.init();
            //产品分类菜单
            if (typeof Util !== 'undefined' && Util.allCategories) Util.allCategories();
            //顶部全局通知手动关闭后不再显示
            if (typeof Util !== 'undefined' && Util.navNotice) Util.navNotice();
            //导航区域-购物车(显隐)
            if (typeof Util !== 'undefined' && Util.navCart) Util.navCart();
        } catch (error) {
            console.warn('初始化错误:', error);
        }

// Cookie同意横幅逻辑
(function() {
    // 等待所有函数定义完成后再执行
    document.addEventListener('DOMContentLoaded', function() {
        const banner = document.getElementById('cookie-consent-banner');
        const acceptBtn = document.getElementById('cookie-accept');
        const declineBtn = document.getElementById('cookie-decline');

        // 检查是否已经做出选择
        const cookieConsent = localStorage.getItem('cookieConsent');

        if (!cookieConsent && banner) {
            // 显示横幅
            banner.style.display = 'block';
        }

        // 接受Cookie
        if (acceptBtn) {
            acceptBtn.addEventListener('click', function() {
                localStorage.setItem('cookieConsent', 'accepted');
                if (banner) banner.style.display = 'none';

                // 加载所有第三方服务
                if (typeof loadGoogleTagManager === 'function') loadGoogleTagManager();
                if (typeof loadMicrosoftClarity === 'function') loadMicrosoftClarity();
                if (typeof loadStatisticalCode === 'function') loadStatisticalCode();
                if (typeof loadGoogleTranslate === 'function') loadGoogleTranslate();
                if (typeof loadGoogleAnalytics === 'function') loadGoogleAnalytics();
            });
        }

        // 拒绝Cookie
        if (declineBtn) {
            declineBtn.addEventListener('click', function() {
                localStorage.setItem('cookieConsent', 'declined');
                if (banner) banner.style.display = 'none';
            });
        }
    });
})();
        </script>

        @stack('scripts_bottom')
        <!-- Google Analytics - 条件加载 -->
        <script>
        function loadGoogleAnalytics() {
            if (localStorage.getItem('cookieConsent') === 'accepted') {
                // 加载gtag.js
                const gtagScript = document.createElement('script');
                gtagScript.async = true;
                gtagScript.src = 'https://www.googletagmanager.com/gtag/js?id=G-0193E7TWL0';
                document.head.appendChild(gtagScript);

                // 初始化gtag
                window.dataLayer = window.dataLayer || [];
                function gtag(){dataLayer.push(arguments);}
                gtag('js', new Date());

                // 配置多个GA ID
                gtag('config', 'G-0193E7TWL0');
                gtag('config', 'G-G906CL8YZ7');
                gtag('config', 'AW-17039594744');

                // 设置全局gtag函数
                window.gtag = gtag;
            }
        }

        // 页面加载时检查
        document.addEventListener('DOMContentLoaded', loadGoogleAnalytics);
        </script>

      <!-- Responsive Popup Code - Perfect Proportions + Mobile Adaption 
<div id="email-popup" style="display:none;position:fixed;top:0;left:0;width:100%;height:100%;background:rgba(0,0,0,0.4);z-index:9999;justify-content:center;align-items:center;font-family:'Nunito', sans-serif;">
    <div style="background:#fff;border-radius:16px;width:90%;max-width:800px;max-height:90vh;display:flex;flex-direction:row;position:relative;overflow:hidden;box-shadow:0 5px 20px rgba(0,0,0,0.08);">
   
        <button id="close-popup" style="position:absolute;top:15px;right:15px;width:32px;height:32px;background:#f5f5f5;border:none;border-radius:50%;color:#888;font-size:18px;cursor:pointer;display:flex;justify-content:center;align-items:center;z-index:2;">×</button>
        
 
        <div style="width:50%;aspect-ratio:1/1;background:#f9f9f9;position:relative;overflow:hidden;">
            <img src="https://images.unsplash.com/photo-1556740738-b6a63e27c4df?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80" 
                 style="width:100%;height:100%;object-fit:cover;">
   
            <div style="position:absolute;bottom:20px;left:20px;background:rgba(255,255,255,0.9);padding:6px 12px;border-radius:16px;font-size:12px;font-weight:600;color:#ff6b8b;box-shadow:0 2px 8px rgba(0,0,0,0.1);">
                ✨ Limited Offer
            </div>
        </div>
        

        <div style="width:50%;padding:30px;box-sizing:border-box;display:flex;flex-direction:column;overflow-y:auto;">
            <div style="margin-bottom:20px;">
                <h2 style="font-size:24px;color:#333;margin:0 0 10px 0;font-weight:600;">Exclusive Deal</h2>
                <p style="color:#666;margin:0;line-height:1.5;font-size:15px;">
                    Get <span style="font-weight:bold;color:#ff6b8b;">$100 OFF</span> on your first order over <span style="font-weight:bold;color:#ff6b8b;">$1,000</span>
                </p>
            </div>
            

            <form id="email-form" style="margin-top:auto;margin-bottom:15px;">
                <div style="margin-bottom:15px;position:relative;">
                    <input type="email" name="email" placeholder="Enter your email" required 
                           style="width:100%;padding:12px 15px;border:1px solid #e0e0e0;border-radius:8px;font-size:14px;background:#fafafa;padding-left:40px;">
                    <span style="position:absolute;left:15px;top:50%;transform:translateY(-50%);color:#aaa;">✉️</span>
                </div>
                <button type="submit" style="width:100%;padding:14px;background:#ff6b8b;color:white;border:none;border-radius:8px;font-size:15px;font-weight:600;cursor:pointer;transition:all 0.2s;">
                    Claim Discount
                </button>
                @csrf
            </form>
            
           
            <div id="success-message" style="display:none;background:#f8f9fa;border-radius:8px;padding:15px;text-align:center;">
                <p style="color:#4CAF50;font-weight:600;margin-bottom:5px;">🎉 Success!</p>
                <p style="color:#666;font-size:13px;">Your discount code has been sent</p>
            </div>
            
            
            <p style="color:#999;font-size:12px;text-align:center;margin-top:15px;">
                We respect your privacy and won't share your email
            </p>
        </div>
    </div>
</div>-->

<!-- 使用本地字体，避免CORS问题 -->

<style>
    #close-popup:hover {
        background: #eee;
        color: #555;
        transform: rotate(90deg);
    }
    
    button[type="submit"]:hover {
        background: #ff5b7d;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(255,107,139,0.3);
    }
    
    input:focus {
        border-color: #ff6b8b !important;
        background: #fff !important;
        outline: none;
        box-shadow: 0 0 0 3px rgba(255,107,139,0.2);
    }
    
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }
    
    /* Responsive Adjustments */
    @media (max-width: 768px) {
        #email-popup > div {
            flex-direction: column;
            max-height: 90vh;
        }
        #email-popup > div > div {
            width: 100% !important;
        }
        #email-popup > div > div:first-child {
            aspect-ratio: 16/9;
            max-height: 40vh;
        }
        #email-popup > div > div:last-child {
            padding: 25px;
            max-height: 60vh;
        }
    }
    
    /* Scrollbar Styling */
    ::-webkit-scrollbar {
        width: 6px;
    }
    ::-webkit-scrollbar-thumb {
        background: rgba(0,0,0,0.1);
        border-radius: 3px;
    }
</style>

    <!--<script>
document.addEventListener('DOMContentLoaded', function() {
    try {
        // 安全检查邮件弹窗元素是否存在
        const emailPopup = document.getElementById('email-popup');
        const closePopup = document.getElementById('close-popup');
        const emailForm = document.getElementById('email-form');

        if (!emailPopup || !closePopup || !emailForm) {
            console.warn('邮件弹窗元素不存在，跳过初始化');
            return;
        }

        // Show popup after 3 seconds
        setTimeout(function() {
            if (emailPopup) {
                emailPopup.style.display = 'flex';
            }
        }, 3000);

        // Close popup
        closePopup.addEventListener('click', function() {
            if (emailPopup) {
                emailPopup.style.display = 'none';
            }
        });

        // Close when clicking outside
        emailPopup.addEventListener('click', function(e) {
            if (e.target === this) {
                this.style.display = 'none';
            }
        });

        // Form submission
        emailForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // Disable button to prevent multiple submissions
            const submitBtn = this.querySelector('button[type="submit"]');
            submitBtn.disabled = true;
            submitBtn.innerHTML = 'Processing...';

            fetch('/submit-email', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('input[name="_token"]').value
                },
                body: JSON.stringify({
                    email: document.querySelector('input[name="email"]').value
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('email-form').style.display = 'none';
                    document.getElementById('success-message').style.display = 'block';

                    setTimeout(function() {
                        document.getElementById('email-popup').style.display = 'none';
                    }, 2000);
                } else {
                    alert(data.message);
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = 'Claim Discount';
                }
            })
            .catch(error => {
                alert('Submission failed, please try again');
                submitBtn.disabled = false;
                submitBtn.innerHTML = 'Claim Discount';
            });
        });
    } catch (error) {
        console.warn('邮件弹窗初始化错误:', error);
    }
});




</script>-->
    </body>
    <!-- Google Tag Manager (noscript) - 条件加载
    <script>
    if (localStorage.getItem('cookieConsent') === 'accepted') {
        document.body.insertAdjacentHTML('beforeend',
            '<noscript class="notranslate"><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-NNHXPH7G" height="0" width="0" style="display:none;visibility:hidden" class="notranslate"></iframe></noscript>'
        );
    }
    </script>
   End Google Tag Manager (noscript) -->

    <!-- translate.js 翻译插件 - 只使用有效的CDN -->
    <script src="https://cdn.staticfile.net/translate.js/3.16.0/translate.js"></script>
    <script>
    // 简单的语言选择函数（全局作用域）
    window.selectLanguage = function(languageCode, displayName) {
       //  console.log('选择语言:', languageCode, displayName);

        // 更新顶部显示
        const currentLangEl = document.getElementById('current-language');
        const currentLangMobileEl = document.getElementById('current-language-mobile');

        if (currentLangEl) {
            currentLangEl.textContent = displayName;
           //  console.log('桌面端显示已更新为:', displayName);
        }
        if (currentLangMobileEl) {
            currentLangMobileEl.textContent = displayName;
           //  console.log('移动端显示已更新为:', displayName);
        }

        // 如果translate.js可用，也执行翻译
        if (typeof translate !== 'undefined' && translate.changeLanguage) {
            translate.changeLanguage(languageCode);
        }
    };
    </script>
    <script>
    // 等待translate.js加载完成
    function initTranslate() {
        if (typeof translate !== 'undefined') {
            // console.log('translate.js加载成功');

            // translate.js加载成功

            // 语言名称映射（使用translate.js支持的语言代码）
            const languageNames = {
                'chinese_simplified': '🇨🇳 简体中文',
                'chinese_traditional': '🇹🇼 繁體中文',
                'english': '🇺🇸 English',
                'japanese': '🇯🇵 日本語',
                'korean': '🇰� 한국어',
                'thai': '🇹🇭 ภាษាไทย',
                'vietnamese': '🇻🇳 Tiếng Việt',
                'malay': '🇲🇾 Bahasa Melayu',
                'indonesian': '🇮🇩 Bahasa Indonesia',
                'spanish': '�🇸 Español',
                'french': '🇫🇷 Français',
                'german': '🇩🇪 Deutsch',
                'russian': '🇷🇺 Русский',
                'portuguese': '🇧🇷 Português',
                'arabic': '🇸🇦 العربية',
                'hindi': '🇮🇳 हिन्दी'
            };

            // 更新语言显示函数
            function updateLanguageDisplay(language) {
                // console.log('开始更新语言显示，语言代码:', language);

                const displayName = languageNames[language] || '🇰🇭 ភាសាខ្មែរ';
                 //console.log('显示名称:', displayName);

                // 更新顶部显示
                const currentLangEl = document.getElementById('current-language');
                const currentLangMobileEl = document.getElementById('current-language-mobile');

                if (currentLangEl) {
                    currentLangEl.textContent = displayName;
                   //  console.log('桌面端顶部已更新');
                }
                if (currentLangMobileEl) {
                    currentLangMobileEl.textContent = displayName;
                   //  console.log('移动端顶部已更新');
                }

                // 更新下拉菜单中的选中状态
                updateMenuSelection(language);

                // console.log('语言显示更新完成');
            }

            // 更新菜单选中状态
            function updateMenuSelection(selectedLanguage) {
                // console.log('更新菜单选中状态:', selectedLanguage);

                // 桌面端菜单
                const desktopMenuItems = document.querySelectorAll('#language-menu a');
                desktopMenuItems.forEach(function(item) {
                    const href = item.getAttribute('href');
                    if (href && href.includes("'" + selectedLanguage + "'")) {
                        item.style.fontWeight = 'bold';
                        item.style.backgroundColor = '#f5f5f5';
                        // console.log('桌面端菜单项已高亮:', selectedLanguage);
                    } else {
                        item.style.fontWeight = 'normal';
                        item.style.backgroundColor = '';
                    }
                });

                // 移动端菜单
                const mobileMenuItems = document.querySelectorAll('.navbar-collapse .dropdown-menu a');
                mobileMenuItems.forEach(function(item) {
                    const href = item.getAttribute('href');
                    if (href && href.includes("'" + selectedLanguage + "'")) {
                        item.style.fontWeight = 'bold';
                        item.style.backgroundColor = '#f5f5f5';
                        // console.log('移动端菜单项已高亮:', selectedLanguage);
                    } else {
                        item.style.fontWeight = 'normal';
                        item.style.backgroundColor = '';
                    }
                });
            }

            // 手动测试函数
            window.testUpdateLanguage = function(lang) {
               //  console.log('手动测试更新语言:', lang);
                updateLanguageDisplay(lang);
            };

            // 简单配置，只加载一次
            translate.language.setLocal('chinese_simplified');
            translate.service.use('client.edge');
            translate.selectLanguageTag.show = false;

            // 忽略跨域iframe和其他可能导致跨域问题的元素
            translate.ignore.tag.push('iframe', 'script', 'style', 'noscript');
            translate.ignore.class.push('notranslate', 'gtm-noscript');

            // 语言切换完成回调
            translate.listener.changeLanguageFinish = function(language) {
                // console.log('🎉 translate.listener.changeLanguageFinish 被触发!');
                // console.log('语言切换完成:', language);
                updateLanguageDisplay(language);
            };

            // 添加其他可能的回调
            translate.listener.renderTaskFinish = function(task) {
                //console.log('🎉 translate.listener.renderTaskFinish 被触发!');
                // console.log('渲染任务完成:', task);

                // 获取当前语言状态
                const currentLanguage = translate.language.getCurrent();
                 //console.log('🔍 当前translate语言状态:', currentLanguage);

                if (currentLanguage && currentLanguage !== 'chinese_simplified') {
                  //   console.log('📝 更新显示到:', currentLanguage);
                   //  updateLanguageDisplay(currentLanguage);
                }
            };

            // 直接监听语言变化（备用方案）
            const originalChangeLanguage = translate.changeLanguage;
            translate.changeLanguage = function(language) {
                // console.log('🔄 translate.changeLanguage 被调用:', language);
                // console.log('🔍 检查语言是否在映射中:', languageNames[language]);
                // console.log('🔍 所有可用语言:', Object.keys(languageNames));

                const result = originalChangeLanguage.call(this, language);

                // 延迟更新显示（确保翻译完成）
                setTimeout(function() {
                  //   console.log('⏰ 延迟更新语言显示:', language);
                  //   console.log('⏰ 当前translate语言状态:', translate.language.getCurrent());
                    updateLanguageDisplay(language);
                }, 1000);

                return result;
            };

            // 添加手动测试所有语言的函数
            window.testAllLanguages = function() {
                const languages = ['khmer', 'thai', 'vietnamese', 'malay', 'indonesian', 'chinese_simplified', 'english'];
                languages.forEach(function(lang, index) {
                    setTimeout(function() {
                        // console.log('🧪 测试语言:', lang);
                        updateLanguageDisplay(lang);
                    }, index * 2000);
                });
            };

            // 只执行一次初始化
            translate.execute();


        } else {
            console.log('translate.js未加载，1秒后重试');
            setTimeout(initTranslate, 1000);
        }
    }

    // 页面加载完成后初始化
    window.addEventListener('load', initTranslate);
    </script>
</html>




