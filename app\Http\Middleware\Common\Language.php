<?php

/**
 * StrongShop
 * <AUTHOR> <<EMAIL>>
 * @license http://www.strongshop.cn/license/
 * @copyright StrongShop Software
 */

namespace App\Http\Middleware\Common;

use Closure;
use App;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Cookie;
use App\Repositories\AppRepository;
use Illuminate\Support\Facades\View;
use App\Repositories\CartRepository;
class Language
{

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $languages = AppRepository::getLanguages();
        $currencies = AppRepository::getCurrencies();
        $route_prefix = $request->route()->getPrefix();
        $currency = $_locale = null;
        if (AppRepository::isWeb() && $route_prefix)
        {
            //根据路由前缀判断语言
            $route_prefix = Str::before($route_prefix, '/');
            $_locale = $route_prefix;
        } elseif ($request->get('locale'))
        {
            //根据url参数判断语言
            $_locale = $request->get('locale');
        } elseif ($request->header('locale'))
        {
            //根据hader头设置判断语言
            $_locale = $request->header('locale');
        } else
        {
            //根据cookie设置判断语言
            $_locale = Cookie::get('locale');

            // 如果没有cookie，尝试根据IP自动检测语言
            if (!$_locale) {
                $_locale = $this->detectLanguageByIP($request->ip());
            }
        }
        $locale = isset($languages[$_locale]) && $languages[$_locale] ? $_locale : config('strongshop.defaultLanguage');

        //货币
        if ($request->get('currency'))
        {
            $currency = $request->get('currency');
        } elseif ($request->header('currency'))
        {
            $currency = $request->header('currency');
        } else
        {
            //根据cookie设置判断语言
            $currency = Cookie::get('currency');
        }
        $currency = isset($currencies[$currency]) && $currencies[$currency] ? $currency : config('strongshop.defaultCurrency');

        App::setLocale($locale); //设置语言

        $response = $next($request);
        if ($locale !== Cookie::get('locale'))
        {
            //设置语言cookie
            $response->cookie('locale', $locale, 60 * 24 * 365);
        }
        if ($currency !== Cookie::get('currency'))
        {
            //设置货币cookie
            $response->cookie('currency', $currency, 60 * 24 * 365);
        }
        $response->header('locale', $locale); //输出hader
        $response->header('currency', $currency); //输出hader
        return $response;
    }

    /**
     * 根据IP检测语言
     */
    private function detectLanguageByIP($ip)
    {
        // 东南亚国家IP段和对应语言映射
        $countryLanguageMap = [
            'KH' => 'km',    // 柬埔寨 - 高棉语
            'TH' => 'th',    // 泰国 - 泰语
            'VN' => 'vi',    // 越南 - 越南语
            'LA' => 'lo',    // 老挝 - 老挝语
            'MM' => 'my',    // 缅甸 - 缅甸语
            'ID' => 'id',    // 印尼 - 印尼语
            'MY' => 'ms',    // 马来西亚 - 马来语
            'SG' => 'en',    // 新加坡 - 英语
            'PH' => 'en',    // 菲律宾 - 英语
            'BN' => 'ms',    // 文莱 - 马来语
            'CN' => 'zh-CN', // 中国 - 简体中文
            'TW' => 'zh-TW', // 台湾 - 繁体中文
            'HK' => 'zh-TW', // 香港 - 繁体中文
            'US' => 'en',    // 美国 - 英语
            'GB' => 'en',    // 英国 - 英语
        ];

        try {
            // 使用免费的IP地理位置API
            $response = @file_get_contents("http://ip-api.com/json/{$ip}?fields=countryCode", false, stream_context_create([
                'http' => [
                    'timeout' => 3, // 3秒超时
                    'method' => 'GET'
                ]
            ]));

            if ($response) {
                $data = json_decode($response, true);
                $countryCode = $data['countryCode'] ?? null;

                if ($countryCode && isset($countryLanguageMap[$countryCode])) {
                    return $countryLanguageMap[$countryCode];
                }
            }
        } catch (\Exception $e) {
            // 如果API调用失败，记录日志但不影响正常流程
            \Log::warning('IP语言检测失败: ' . $e->getMessage());
        }

        // 备用方案：根据浏览器Accept-Language头检测
        return $this->detectLanguageByBrowser();
    }

    /**
     * 根据浏览器语言检测
     */
    private function detectLanguageByBrowser()
    {
        $acceptLanguage = $_SERVER['HTTP_ACCEPT_LANGUAGE'] ?? '';

        // 解析Accept-Language头
        if (preg_match('/zh-CN|zh-Hans/i', $acceptLanguage)) {
            return 'zh-CN';
        } elseif (preg_match('/zh-TW|zh-Hant/i', $acceptLanguage)) {
            return 'zh-TW';
        } elseif (preg_match('/km/i', $acceptLanguage)) {
            return 'km';
        } elseif (preg_match('/th/i', $acceptLanguage)) {
            return 'th';
        } elseif (preg_match('/vi/i', $acceptLanguage)) {
            return 'vi';
        } elseif (preg_match('/en/i', $acceptLanguage)) {
            return 'en';
        }

        // 默认返回中文
        return 'zh-CN';
    }

}
