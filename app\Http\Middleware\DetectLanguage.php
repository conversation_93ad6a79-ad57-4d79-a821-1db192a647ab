<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Session;

class DetectLanguage
{
    /**
     * 东南亚国家IP段和对应语言映射
     */
    private $countryLanguageMap = [
        'KH' => 'km',    // 柬埔寨 - 高棉语
        'TH' => 'th',    // 泰国 - 泰语
        'VN' => 'vi',    // 越南 - 越南语
        'LA' => 'lo',    // 老挝 - 老挝语
        'MM' => 'my',    // 缅甸 - 缅甸语
        'ID' => 'id',    // 印尼 - 印尼语
        'MY' => 'ms',    // 马来西亚 - 马来语
        'SG' => 'en',    // 新加坡 - 英语
        'PH' => 'en',    // 菲律宾 - 英语
        'BN' => 'ms',    // 文莱 - 马来语
        'CN' => 'zh-CN', // 中国 - 简体中文
        'TW' => 'zh-TW', // 台湾 - 繁体中文
        'HK' => 'zh-TW', // 香港 - 繁体中文
        'US' => 'en',    // 美国 - 英语
        'GB' => 'en',    // 英国 - 英语
    ];

    /**
     * 支持的语言列表
     */
    private $supportedLanguages = [
        'zh-CN', 'en', 'km', 'th', 'vi', 'lo', 'my', 'id', 'ms', 'zh-TW'
    ];

    public function handle(Request $request, Closure $next)
    {
        // 如果用户已经手动选择了语言，优先使用用户选择
        if ($request->has('locale')) {
            $locale = $request->get('locale');
            if (in_array($locale, $this->supportedLanguages)) {
                Session::put('locale', $locale);
                App::setLocale($locale);
                return $next($request);
            }
        }

        // 检查Session中是否已有语言设置
        if (Session::has('locale')) {
            $locale = Session::get('locale');
            if (in_array($locale, $this->supportedLanguages)) {
                App::setLocale($locale);
                return $next($request);
            }
        }

        // 根据IP检测国家并设置语言
        $detectedLanguage = $this->detectLanguageByIP($request->ip());
        
        if ($detectedLanguage) {
            Session::put('locale', $detectedLanguage);
            App::setLocale($detectedLanguage);
        } else {
            // 默认使用中文
            Session::put('locale', 'zh-CN');
            App::setLocale('zh-CN');
        }

        return $next($request);
    }

    /**
     * 根据IP检测语言
     */
    private function detectLanguageByIP($ip)
    {
        try {
            // 使用免费的IP地理位置API
            $response = @file_get_contents("http://ip-api.com/json/{$ip}?fields=countryCode");
            
            if ($response) {
                $data = json_decode($response, true);
                $countryCode = $data['countryCode'] ?? null;
                
                if ($countryCode && isset($this->countryLanguageMap[$countryCode])) {
                    return $this->countryLanguageMap[$countryCode];
                }
            }
        } catch (\Exception $e) {
            // 如果API调用失败，记录日志但不影响正常流程
            \Log::warning('IP语言检测失败: ' . $e->getMessage());
        }

        // 备用方案：根据浏览器Accept-Language头检测
        return $this->detectLanguageByBrowser();
    }

    /**
     * 根据浏览器语言检测
     */
    private function detectLanguageByBrowser()
    {
        $acceptLanguage = $_SERVER['HTTP_ACCEPT_LANGUAGE'] ?? '';
        
        // 解析Accept-Language头
        if (preg_match('/zh-CN|zh-Hans/i', $acceptLanguage)) {
            return 'zh-CN';
        } elseif (preg_match('/zh-TW|zh-Hant/i', $acceptLanguage)) {
            return 'zh-TW';
        } elseif (preg_match('/km/i', $acceptLanguage)) {
            return 'km';
        } elseif (preg_match('/th/i', $acceptLanguage)) {
            return 'th';
        } elseif (preg_match('/vi/i', $acceptLanguage)) {
            return 'vi';
        } elseif (preg_match('/en/i', $acceptLanguage)) {
            return 'en';
        }

        // 默认返回中文
        return 'zh-CN';
    }
}
